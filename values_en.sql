INSERT INTO unit (id, name, short_name, category) VALUES
  (1, 'gram', 'g', 'weight'),
  (2, 'kilogram', 'kg', 'weight'),
  (3, 'decagram', 'dkg', 'weight'),
  (4, 'milliliter', 'ml', 'volume'),
  (5, 'centiliter', 'cl', 'volume'),
  (6, 'deciliter', 'dl', 'volume'),
  (7, 'liter', 'l', 'volume'),
  (8, 'piece', 'pc', 'other'),
  (9, 'tablespoon', 'tbsp', 'other'),
  (10, 'teaspoon', 'tsp', 'other'),
  (11, 'pinch', 'pinch', 'other'),
  (12, 'slice', 'slice', 'other'),
  (13, 'cup', 'cup', 'other');

  INSERT INTO category (id, name) VALUES
  (1, 'Meal time'),
  (2, 'Origin'),
  (3, 'Food pyramid'),
  (4, 'Processing method'),
  (5, 'Cuisine'),
  (6, 'Taste'),
  (7, 'Diet criteria');

  -- Meal time (cat 1)
INSERT INTO tag (id, name, category) VALUES
  (1, 'Breakfast', 1),
  (2, 'Lunch', 1),
  (3, 'Dinner', 1),
  (4, 'Snack', 1);

-- Origin (cat 2)
INSERT INTO tag (id, name, category) VALUES
  (5, 'Plant-based', 2),
  (6, 'Animal-based', 2),
  (7, 'Fungi', 2);

-- Food pyramid (cat 3)
INSERT INTO tag (id, name, category) VALUES
  (8, 'Grains', 3),
  (9, 'Fruits', 3),
  (10, 'Vegetables', 3),
  (11, 'Dairy', 3),
  (12, 'Meat/Fish/Eggs/Legumes', 3),
  (13, 'Fats/Oils', 3),
  (14, 'Sweets/Snacks', 3);

-- Processing method (cat 4)
INSERT INTO tag (id, name, category) VALUES
  (15, 'Raw', 4),
  (16, 'Dried', 4),
  (17, 'Frozen', 4),
  (18, 'Boiled', 4),
  (19, 'Baked', 4),
  (20, 'Fried', 4),
  (21, 'Fermented', 4),
  (22, 'Canned', 4);

-- Cuisine (cat 5)
INSERT INTO tag (id, name, category) VALUES
  (23, 'Italian', 5),
  (24, 'French', 5),
  (25, 'Spanish', 5),
  (26, 'Central European', 5),
  (27, 'Scandinavian', 5),
  (28, 'Chinese', 5),
  (29, 'Indian', 5),
  (30, 'Japanese', 5),
  (31, 'Thai', 5),
  (32, 'Korean', 5),
  (33, 'Arabic', 5),
  (34, 'Turkish', 5),
  (35, 'North American', 5),
  (36, 'Mexican', 5),
  (37, 'South American', 5),
  (38, 'African', 5),
  (39, 'Australian', 5);

-- Taste (cat 6)
INSERT INTO tag (id, name, category) VALUES
  (40, 'Sweet', 6),
  (41, 'Salty', 6),
  (42, 'Sour', 6),
  (43, 'Bitter', 6),
  (44, 'Umami', 6),
  (45, 'Spicy', 6);

-- Diet criteria (cat 7)
INSERT INTO tag (id, name, category) VALUES
  (46, 'Vegetarian', 7),
  (47, 'Vegan', 7),
  (48, 'Gluten-free', 7),
  (49, 'Lactose-free', 7),
  (50, 'Keto', 7),
  (51, 'Halal', 7);