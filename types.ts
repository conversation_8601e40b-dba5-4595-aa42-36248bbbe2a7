interface Ingredient {
  id: string
  name: string
}

interface Recipe {
  id: string
  title: string
  description?: string
  cook_duration?: number
  servings?: number
  created_at: string
  updated_at: string
}

interface RecipeImage {
  id: string
  recipe_id: string
  url: string
  created_at: string
  description?: string
}

interface Unit {
  id: string
  name: string
  short_name: string
  category: string // todo enum
}

interface RecipeIngredient {
  id: string
  recipe_id: string
  ingredient_id: string
  quantity?: number
  unit_id?: string
}

interface RecipeStep {
  id: string
  recipe_id: string
  step_number: number
  created_at: string
  text: string
}

interface RecipeStepIngredient {
  id: string
  step_id: string
  ingredient_id: string
  quantity_used?: number
}

interface Category {
  id: string
  name: string
}

interface Tag {
  id: string
  name: string
  category_id: string
}

interface RecipeTag {
  id: string
  recipe_id: string
  tag_id: string
  created_at: string
}

interface FavoriteRecipe {
  id: string
  recipe_id: string
  created_at: string
}

interface RecipeCalendar {
  id: string
  recipe_id: string
  cooked_at: string
}
