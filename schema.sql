CREATE TABLE ingredient (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE
);

CREATE TABLE recipe (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  cook_duration INTEGER,
  servings INTEGER,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);

CREATE TABLE recipe_image (
  id TEXT PRIMARY KEY,
  recipe_id TEXT NOT NULL REFERENCES recipe(id) ON DELETE CASCADE,
  url TEXT NOT NULL,
  created_at TEXT NOT NULL,
  description TEXT
);

CREATE TABLE unit (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  short_name TEXT NOT NULL UNIQUE,
  category TEXT NOT NULL DEFAULT 'other' CHECK (category IN (
    'weight',
    'volume',
    'other' -- ks, tbsp, tsp, špetka, plátek, hrnek
  ))
);

CREATE TABLE recipe_ingredient (
  id TEXT PRIMARY KEY,
  recipe_id TEXT NOT NULL REFERENCES recipe(id) ON DELETE CASCADE,
  ingredient_id TEXT NOT NULL REFERENCES ingredient(id),
  quantity INTEGER,
  unit_id TEXT REFERENCES unit(id),
  UNIQUE (recipe_id, ingredient_id)
);

CREATE TABLE recipe_step (
  id TEXT PRIMARY KEY,
  recipe_id TEXT NOT NULL REFERENCES recipe(id) ON DELETE CASCADE,
  step_number INTEGER NOT NULL,
  created_at TEXT NOT NULL,
  text TEXT NOT NULL,
  UNIQUE(recipe_id, step_number)
);

CREATE TABLE recipe_step_ingredient (
  id TEXT PRIMARY KEY,
  recipe_step_id TEXT NOT NULL REFERENCES recipe_step(id) ON DELETE CASCADE,
  recipe_ingredient_id TEXT NOT NULL REFERENCES recipe_ingredient(id),
  quantity_used INTEGER
);

CREATE TABLE category (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE
);

CREATE TABLE tag (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  category_id TEXT NOT NULL REFERENCES category(id),
);

CREATE TABLE recipe_tag (
  id TEXT PRIMARY KEY,
  recipe_id TEXT NOT NULL REFERENCES recipe(id) ON DELETE CASCADE,
  tag_id TEXT NOT NULL REFERENCES tag(id),
  created_at TEXT NOT NULL,
  UNIQUE (recipe_id, tag_id)
);

CREATE TABLE favorite_recipe (
  id TEXT PRIMARY KEY,
  recipe_id TEXT NOT NULL REFERENCES recipe(id) ON DELETE CASCADE,
  created_at TEXT NOT NULL,
  UNIQUE(recipe_id, user_id)
);

CREATE TABLE recipe_calendar (
  id TEXT PRIMARY KEY,
  recipe_id TEXT NOT NULL REFERENCES recipe(id) ON DELETE CASCADE,
  cooked_at TEXT NOT NULL
);
